// Happ VPN RSA şifreleme fonksiyonu
function encryptWithHappRSA(linkToEncrypt) {
  try {
    const apiUrl = 'https://crypto.happ.su/api.php';

    const payload = {
      'method': 'POST',
      'headers': {
        'Content-Type': 'application/json'
      },
      'payload': JSON.stringify({
        'url': linkToEncrypt
      })
    };

    const response = UrlFetchApp.fetch(apiUrl, payload);

    // Debug için response'u logla
    console.log(`Happ API Response Code: ${response.getResponseCode()}`);
    console.log(`Happ API Response: ${response.getContentText()}`);
    console.log(`Original link: ${linkToEncrypt}`);

    if (response.getResponseCode() === 200) {
      const result = response.getContentText().trim();
      // Happ API'den dönen sonucu happ:// formatında döndür
      return `happ://${result}`;
    } else {
      console.log(`Happ API hatası, orijinal link döndürülüyor: ${response.getResponseCode()}`);
      return linkToEncrypt; // Hata durumunda orijinal linki döndür
    }
  } catch (error) {
    console.error('Happ RSA şifreleme hatası:', error);
    return linkToEncrypt; // Hata durumunda orijinal linki döndür
  }
}

// Test fonksiyonu - Happ API'yi test etmek için
function testHappAPI() {
  const testLink = "vless://<EMAIL>:443?type=tcp#test";
  const result = encryptWithHappRSA(testLink);
  console.log(`Test sonucu: ${result}`);
  return result;
}

function doGet(e) {
    const subUrl = e.parameter.url;
    if (!subUrl) {
      return ContentService
        .createTextOutput("Error: missing ?url= parameter")
        .setMimeType(ContentService.MimeType.TEXT);
    }

    try {
      // Fetch subscription and headers
      const resp = UrlFetchApp.fetch(subUrl, { muteHttpExceptions: true });
      if (resp.getResponseCode() !== 200) {
        throw new Error("HTTP " + resp.getResponseCode());
      }

      // Get config body (usually base64-encoded or direct)
      const b64 = resp.getContentText().trim();
      let decoded;
      try {
        decoded = Utilities
          .newBlob(Utilities.base64Decode(b64))
          .getDataAsString()
          .split(/\r?\n/);
      } catch (err) {
        decoded = b64.split(/\r?\n/);
      }

      // Get usage stats from response header
      const headers = resp.getAllHeaders();
      let upload = 0, download = 0, total = 0, expire = 0;
      if (headers['subscription-userinfo'] || headers['Subscription-Userinfo']) {
        const info = headers['subscription-userinfo'] || headers['Subscription-Userinfo'];
        const match = info.match(/upload=(\d+); *download=(\d+); *total=(\d+); *expire=(\d+)/);
        if (match) {
          upload = match[1];
          download = match[2];
          total = match[3];
          expire = match[4];
        }
      }

      const configs = decoded.filter(line =>
        /^(ss|vless|vmess|trojan):\/\//.test(line.trim())
      );

      const output = [
        "#profile-title: base64:QkVTVCBUS00gUE9EUElTS0E=",
        "#profile-update-interval: 1",
        "#support-url: https://t.me/nerwa_degme",
        "#profile-web-page-url: https://t.me/nerwa_degme",
        "#announce: base64:8J+kkSBwb2RwaXNrYSBWcG5sYXLwn5iICuKAlCBUZXN0OiBCQSAoNjAtODBtcynimJHvuI8K4oCUIEhlcGRlOiA2MFRNVCDwn5K1CuKAlCBBeWx5azogMTgwIFRNVCDwn5K1CvCfjJ8gSGFwcCBQb2RwaXNrYSBhbmRyb2lkwqAg8J+Mnwrwn4yfIFNoYWRvd3NvY2tzIGFuZHJvaWQg8J+Mnwrwn4yfIE52cCB0dW5lbCBwb2RwaXNrYSDwn4yfIC8gaW9z8J+MnwrigJQgMSBnw7xuOiAxMFRNVCDwn5K1CuKAlCAyIGfDvG46IDIwVE1UIPCfkrUK4oCUIDcgZ8O8bjogNjBUTVQg8J+StQrigJQgMzDCoCBnw7xuOiAxODBUTVQg8J+StQrwn5KsIFRHIOKAlCBAbmVyd2FfZGVnbWUK8J+MnyBMxLBOSyDigJQgQG5lcndhX2RlZ21lCvCfjJ8gU1RBUlQg4oCUIEBuZXJ3YV9kZWdtZQrwn5KsIE9UWllXIOKAlCBAbmVyd2FfZGVnbWVfb3R6eXc=",
        `#subscription-userinfo: upload=${upload}; download=${download}; total=${total}; expire=${expire}`,
        ...configs.map(config => encryptWithHappRSA(config.trim()))
      ].join('\n');

      // Encode the whole output as base64
      const encodedOutput = Utilities.base64Encode(output);

      return ContentService
        .createTextOutput(encodedOutput)
        .setMimeType(ContentService.MimeType.TEXT);

    } catch (err) {
      return ContentService
        .createTextOutput("Error: " + err.message)
        .setMimeType(ContentService.MimeType.TEXT);
    }
  
}