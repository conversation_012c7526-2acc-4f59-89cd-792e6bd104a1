// Happ VPN RSA şifreleme fonksiyonu
function encryptWithHappRSA(linkToEncrypt) {
  try {
    const apiUrl = 'https://crypto.happ.su/api.php';

    // Link uzunluğuna göre RSA key seç (110 karakter altı RSA-1024, üstü RSA-4096)
    const useRSA4096 = linkToEncrypt.length > 110;
    const publicKey = useRSA4096 ?
      `-----B<PERSON>IN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA5cL2yu9dZGnNbs4jt222
NugIqiuZdXKdTh4IgXZmOX0vdpW+rYWrPd1EObQ3Urt+YBTK5Di98EBjYCPr8tus
aVRAn3Vaq41CDisEdX35u1N8jSHQ0zDOtPdrvJtlqShib4UI6Vybk/QSmoZVbpRb
67TNsiFqBmK1kxT+mbtHkhdT2u+hzNLQr0FtJR1+gC+ELKZ48zZY/d3YSSRSb+dx
Und4FH31Kz68VKqlajISSzIrGQWc/zqSlihIvfnTPNX3pCyJpwAuYXieWSRDAogr
wGwoiN++y14OLYHrNlqzoJ44WM3Tbm7x1Dj/8QI3tzwixli/0JmqQ19ssETDbVQ9
0asoPc4QFhyc4c+PH62AdK1S+ysXt5uqEujRBk3rC53l65IOVXSTZgsLwzS7EFY9
lZszJXUJJh5GB9heO8c7PNCTOxno3l4684iHFJuxnkS0DLbdzCXfovwfIP8q3lj7
UJswPKVHkCLNSUutNke+xex1J3YEdvebJzv7Dk78PqLRmLWaEsAhQanXs93aTxEk
d/p7hgFV30QozVQ/oNAvmQSVIBd6zCGM3of3R3tmDkDNGQGrY4MBTX+cTJGYstdh
QXxj1oFZEG16F/0GGXG+sia67gYM3OC7RWyBOzULsEmupIiM8Vdx1iErw7yvJSC4
IsIsWZD8JAmZtLBqEQ/TvfcCAwEAAQ==
-----END PUBLIC KEY-----` :
      `-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCxsS7PUq1biQlVD92rf6eXKr9o
G1/SrYx3qWahZP+Jq35m4Wb/Z+mB6eBWrPzJ/zZpZLWLQorcvOKt+sLaCHyH1HLN
kti4jlaEQX6x97XgBm8GK08+lLLWquFDhWRNxsrfzJyNdpVopzBRmCJKTc8ObYyP
brv9T35a8Kd5WqjnUwIDAQAB
-----END PUBLIC KEY-----`;

    const payload = {
      'method': 'POST',
      'headers': {
        'Content-Type': 'application/json'
      },
      'payload': JSON.stringify({
        'url': linkToEncrypt
      })
    };

    const response = UrlFetchApp.fetch(apiUrl, payload);

    if (response.getResponseCode() === 200) {
      const result = response.getContentText().trim();
      // Happ API'den dönen sonucu happ:// formatında döndür
      return `happ://${result}`;
    } else {
      throw new Error(`Happ API hatası: ${response.getResponseCode()}`);
    }
  } catch (error) {
    console.error('Happ RSA şifreleme hatası:', error);
    return linkToEncrypt; // Hata durumunda orijinal linki döndür
  }
}

function doGet(e) {
    const subUrl = e.parameter.url;
    if (!subUrl) {
      return ContentService
        .createTextOutput("Error: missing ?url= parameter")
        .setMimeType(ContentService.MimeType.TEXT);
    }

    try {
      // Fetch subscription and headers
      const resp = UrlFetchApp.fetch(subUrl, { muteHttpExceptions: true });
      if (resp.getResponseCode() !== 200) {
        throw new Error("HTTP " + resp.getResponseCode());
      }

      // Get config body (usually base64-encoded or direct)
      const b64 = resp.getContentText().trim();
      let decoded;
      try {
        decoded = Utilities
          .newBlob(Utilities.base64Decode(b64))
          .getDataAsString()
          .split(/\r?\n/);
      } catch (err) {
        decoded = b64.split(/\r?\n/);
      }

      // Get usage stats from response header
      const headers = resp.getAllHeaders();
      let upload = 0, download = 0, total = 0, expire = 0;
      if (headers['subscription-userinfo'] || headers['Subscription-Userinfo']) {
        const info = headers['subscription-userinfo'] || headers['Subscription-Userinfo'];
        const match = info.match(/upload=(\d+); *download=(\d+); *total=(\d+); *expire=(\d+)/);
        if (match) {
          upload = match[1];
          download = match[2];
          total = match[3];
          expire = match[4];
        }
      }

      const configs = decoded.filter(line =>
        /^(ss|vless|vmess|trojan):\/\//.test(line.trim())
      );

      const output = [
        "#profile-title: base64:QkVTVCBUS00gUE9EUElTS0E=",
        "#profile-update-interval: 1",
        "#support-url: https://t.me/nerwa_degme",
        "#profile-web-page-url: https://t.me/nerwa_degme",
        "#announce: base64:8J+kkSBwb2RwaXNrYSBWcG5sYXLwn5iICuKAlCBUZXN0OiBCQSAoNjAtODBtcynimJHvuI8K4oCUIEhlcGRlOiA2MFRNVCDwn5K1CuKAlCBBeWx5azogMTgwIFRNVCDwn5K1CvCfjJ8gSGFwcCBQb2RwaXNrYSBhbmRyb2lkwqAg8J+Mnwrwn4yfIFNoYWRvd3NvY2tzIGFuZHJvaWQg8J+Mnwrwn4yfIE52cCB0dW5lbCBwb2RwaXNrYSDwn4yfIC8gaW9z8J+MnwrigJQgMSBnw7xuOiAxMFRNVCDwn5K1CuKAlCAyIGfDvG46IDIwVE1UIPCfkrUK4oCUIDcgZ8O8bjogNjBUTVQg8J+StQrigJQgMzDCoCBnw7xuOiAxODBUTVQg8J+StQrwn5KsIFRHIOKAlCBAbmVyd2FfZGVnbWUK8J+MnyBMxLBOSyDigJQgQG5lcndhX2RlZ21lCvCfjJ8gU1RBUlQg4oCUIEBuZXJ3YV9kZWdtZQrwn5KsIE9UWllXIOKAlCBAbmVyd2FfZGVnbWVfb3R6eXc=",
        `#subscription-userinfo: upload=${upload}; download=${download}; total=${total}; expire=${expire}`,
        ...configs.map(config => encryptWithHappRSA(config.trim()))
      ].join('\n');

      // Encode the whole output as base64
      const encodedOutput = Utilities.base64Encode(output);

      return ContentService
        .createTextOutput(encodedOutput)
        .setMimeType(ContentService.MimeType.TEXT);

    } catch (err) {
      return ContentService
        .createTextOutput("Error: " + err.message)
        .setMimeType(ContentService.MimeType.TEXT);
    }
  
}